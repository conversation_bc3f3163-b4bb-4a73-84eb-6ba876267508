@if (!isCustom) {
  <div class="parent-container" [ngClass]="showSelected ? 'selected' : 'normal'">
    @if (type !== 'password') {
      <div class="floating-input-container" [class.focused]="isFocused || textValue" [style.width]="'500px'">
        <input
          class="floating-input"
          [ngClass]="{ 'no-padding': noPadding, 'no-border': noBorder, 'side-panel-input-padding': sidePanelPadding }"
          [attr.title]="toolTip"
          [attr.maxlength]="maxLength"
          [style.background]="backgroundColor"
          (keyup)="valueChanged($event)"
          (focus)="isFocused = true"
          (blur)="isFocused = false"
          [placeholder]="''"
          [disabled]="disabled"
          [value]="textValue"
          [type]="type"
          style="height:56px;"
        />
        <label class="floating-label">
          {{ label }}
          @if (required) {
            <span class="reqAsterisk">*</span>
          }
        </label>
        @if (options && options.length > 0) {
          <ion-button fill="clear" slot="end" (click)="openOptionsDropdown($event)">
            {{ selectedOption || 'Default Style' }}
          </ion-button>
        }
      </div>
    }
    @if (type === 'password') {
      <ion-item lines="none" class="inner-container" [ngClass]="{ 'no-padding': noPadding, 'no-border': noBorder, 'side-panel-input-padding': sidePanelPadding }" [title]="toolTip" #control>
        @if (showSelected !== true) {
          <ion-label position="stacked" class="label-header">
            {{ label }}
            <span title="text" class="reqAsterisk">
              @if (required) {
                <span>*</span>
              }
              @if (toolTip) {
                <ion-icon name="information-circle-outline"></ion-icon>
              }
            </span>
            @if (identifierText) {
              <span class="identifier">{{ identifierText }}</span>
            }
          </ion-label>
        }
        <ng-container>
          <div class="password-container" [style]="'background: ' + backgroundColor + '; border-color:' + borderColor">
            <ion-input
              [maxlength]="maxLength"
              (keyup)="valueChanged($event)"
              [placeholder]="placeHolder"
              [disabled]="disabled"
              [value]="textValue"
              [type]="showPassword === true ? 'text' : 'password'">
            </ion-input>
            <a class="type-toggle" (click)="togglePasswordVisibility()">
              <ion-icon class="show-option" title="Show password" [hidden]="showPassword" name="eye-off-outline"></ion-icon>
              <ion-icon class="hide-option" title="Hide password" [hidden]="!showPassword" name="eye-outline"></ion-icon>
            </a>
          </div>
        </ng-container>
      </ion-item>
    }
    @if (errorMessage && touched === true && showSelected) {
      <ion-text>
        {{ errorMessage }}
      </ion-text>
    }
  </div>
}

@if (isCustom) {
  <ion-input [style.--background-color]="backgroundColor" (keyup)="valueChanged($event)" [placeholder]="placeHolder" [disabled]="disabled" [value]="textValue" [type]="type"></ion-input>
  @if (errorMessage && touched === true) {
    <ion-text>
      {{ errorMessage }}
    </ion-text>
  }
}
