import { NgClass } from '@angular/common';
import { Component, EventEmitter, forwardRef, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IonicModule, PopoverController } from '@ionic/angular';
import { MaskitoModule } from '@maskito/angular';
import { MaskitoElementPredicateAsync, MaskitoOptions } from '@maskito/core';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { SuffixPopoverComponent } from '@app/standalone/components/suffix-popover/suffix-popover.component';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';
import { IComponent } from '@app/core/contracts/contract';
@Component({
    selector: 'app-text-input-control-assignment',
    templateUrl: './text-input-control-assignment.component.html',
    styleUrls: ['./text-input-control-assignment.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => TextInputControlComponentAssignment),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => TextInputControlComponentAssignment),
        },
    ],
    imports: [NgClass, IonicModule, MaskitoModule]
})
export class TextInputControlComponentAssignment extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() component: IComponent;
  @Input() toolTip!: string;
  @Input() placeHolder!: string;
  @Input() override label!: string;
  @Input() override disabled = false;
  @Input() backgroundColor = '#181818';
  @Input() itemBackgroundColor = '';
  @Input() type = 'text';
  @Input() identifierText: string;
  @Input() isCustom = false;
  @Input() showSelected = false;
  @Input() maxLength: number;
  @Input() limit: number;
  @Input() noPadding = false;
  @Input() noBorder = false;
  @Input() sidePanelPadding = false;
  @Input() defaultValue: string | undefined;
  @Input() options: any;
  @Input() selectedOption: string;
  @Input() borderColor: string | undefined;
  @Input() min: number;
  @Output() textChanged = new EventEmitter();
  @Output() optionsValueChanged = new EventEmitter<string>();

  showPassword = false;
  componentDestroyed$: Subject<boolean> = new Subject();
  private inputChanged$ = new Subject<string>();
  isFocused = false;

  constructor(
    private popoverController: PopoverController,
    private signalService: ComponentUpdateSignalService
  ) {
    super();
  }

  readonly phoneMask: MaskitoOptions = {
    mask: ['(', /\d/, /\d/, /\d/, ')', ' ', /\d/, /\d/, /\d/, '-', /\d/, /\d/, /\d/, /\d/],
  };
  readonly maskPredicate: MaskitoElementPredicateAsync = async el => (el as HTMLIonInputElement).getInputElement();

  ngOnInit() {
    this.setDefaultValue();
    this.fieldValueChanged.pipe(takeUntil(this.componentDestroyed$)).subscribe(event => {
      this.textChanged.emit(event);
    });

    this.inputChanged$.pipe(debounceTime(500), takeUntil(this.componentDestroyed$)).subscribe((event: any) => {
      this.setValue(event);
      if (this.component?.id) {
        this.signalService.triggerSignal({ componentId: this.component.id, updateValue: event.target.value });
      }
    });
  }

  valueChanged(event: any) {
    this.inputChanged$.next(event);
  }

  setDefaultValue() {
    setTimeout(() => {
      if (this.defaultValue && !this.textValue) {
        this.textValue = this.defaultValue;
      }
    }, 1);
  }

  async openOptionsDropdown(ev: Event) {
    const popover = await this.popoverController.create({
      component: SuffixPopoverComponent,
      cssClass: 'popover',
      event: ev,
      translucent: true,
      componentProps: {
        options: this.options,
        selected: this.options.find(x => x.value === this.selectedOption).value,
      },
    });
    await popover.present();
    const { data } = await popover.onDidDismiss();
    if (data !== undefined) {
      this.selectedOption = data;
      this.optionsValueChanged.next(data);
    }
  }

  numberOnlyValidation(event: any) {
    const pattern = /[0-9.,]/;
    const inputChar = String.fromCharCode(event.charCode);

    if (!pattern.test(inputChar)) {
      event.preventDefault();
    }
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
  }
}
