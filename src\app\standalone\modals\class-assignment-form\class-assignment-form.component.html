<ion-header class="ion-no-border">
  <ion-toolbar mode="ios">
    <ion-title>{{ instance ? 'Edit' : 'Create' }} {{ header }}</ion-title>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding" [style.height.vh]="height" [style.min-height.vh]="height" [style.max-height.vh]="height">
  @if (classForm) {
    <form [formGroup]="classForm">
      <ion-grid>
        <ion-row>
          <ion-col size="12">
            <app-text-input-control-assignment
              [backgroundColor]="'#1E1E1E'"
              [noPadding]="true"
              [label]="header + ' Name'"
              [placeHolder]="'e.g. Careers 101'"
              [toolTip]="'Add the class name'"
              [borderColor]="'#000000'"
              formControlName="name"></app-text-input-control-assignment>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col size="12">
            <app-text-area-input-control
              [backgroundColor]="'#1E1E1E'"
              [noPadding]="true"
              [label]="'Description'"
              [placeHolder]="'Enter ' + header + ' description'"
              [toolTip]="'Description'"
              formControlName="description"></app-text-area-input-control>
            <ion-label class="subtext">Optional help students learn what this {{ header | lowercase }} is about.</ion-label>
          </ion-col>
        </ion-row>
        @if (featureTypeName === 'Modifiable Learning Container Pages') {
          <ion-row>
            <ion-col [size]="educatorsExist === true ? 6 : 12">
              @if (grades$ | async; as grades) {
                <app-tag-select-option-control
                  [noPadding]="true"
                  [options]="grades"
                  [label]="'Grade'"
                  [placeHolder]="'--select--'"
                  formControlName="gradeId"
                  [backgroundColor]="'#1e1e1e'"
                  [limitTo]="2"
                  [toolTip]="'Choose the grade'"></app-tag-select-option-control>
              }
            </ion-col>
            <ion-col size="6">
              @if (users$ | async; as users) {
                <app-select-option-control
                  [noPadding]="true"
                  [toolTip]="'Select Educator'"
                  [placeHolder]="'--select--'"
                  [label]="'Educator'"
                  [formControlName]="'educatorId'"
                  [backgroundColor]="'#1E1E1E'"
                  [options]="users"></app-select-option-control>
              }
            </ion-col>
          </ion-row>
        } @else {
          <ion-row>
            <ion-col size="12">
              <ion-item class="due-date-item">
                <ion-label position="stacked"> Due Date </ion-label>
                <ion-input placeholder="Select a date" value="{{ getFormControl('dueDate').value | date: 'YYYY/MM/dd' }}" id="date" [readonly]="true">
                  <ion-icon class="calendar-icon" name="calendar-outline" item-right></ion-icon>
                </ion-input>
                <ion-popover #popover trigger="date" side="top" id="popover-bottom" alignment="end">
                  <ng-template>
                    <ion-content><ion-datetime (ionChange)="onDateSelected(popover)" formControlName="dueDate" presentation="date" displayFormat="yyyy/mm/dd"></ion-datetime></ion-content>
                  </ng-template>
                </ion-popover>
              </ion-item>
            </ion-col>
          </ion-row>
        }
        <ion-row>
          <ion-col size="12">
            <app-file-upload-control formControlName="thumbnailId" [label]="'Thumbnail'" [fileTypeBw]="8192" [toolTip]="'The class thumbnail'"></app-file-upload-control>
            <ion-label class="subtext">Optional add a thumbnail! Upload a JPG that is at least 500px wide and has a 3:2 aspect ratio.</ion-label>
          </ion-col>
        </ion-row>
      </ion-grid>
    </form>
  }
</ion-content>
@if (classForm) {
  <ion-footer>
    <ion-toolbar mode="ios">
      <ion-buttons slot="secondary">
        <ion-button class="assign-button" fill="clear" color="medium" (click)="cancel()"> Cancel </ion-button>
      </ion-buttons>
      <ion-buttons slot="primary">
        <ion-button class="assign-button" fill="solid" color="primary" [disabled]="classForm.valid !== true || loading === true" (click)="saveClicked()">
          {{ instance != null ? 'Save' : 'Create' }}
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-footer>
}
@if (loading === true) {
  <ion-grid class="scormloader">
    <ion-row class="ion-align-items-center">
      <ion-col size="12">
        <img class="img" src="assets/images/EdgeFactor-EF_rings-2018-white_small_png.png" />
        <br />
        <ion-spinner color="primary" name="dots"></ion-spinner>
      </ion-col>
    </ion-row>
  </ion-grid>
}
