ion-searchbar {
  --background: #2B2B2B;
  --border-radius: 20px;
  width: 90%;
  margin-right: 5%;
  margin-left: 5%;
}

ion-item {
  --padding-start: 0;
}

ion-header {
  ion-toolbar {
    --background: #323232;
  }

  ion-title {
    margin-left: 16px;
    margin-right: 16px;
    margin-top: 30px;
    font-size: 40px;
    text-align: left;
    padding-inline: 0px !important;
    font-weight: 700;
    font-family: 'Exo 2';
  }

  ion-button {
    --color: #606060;
  }
}

ion-footer {
  border-top: 1px solid #1E1E1E;

  ion-toolbar {
    --background: #323232;
    padding-top: 5px;
    padding-bottom: 10px;
  }

  ion-button {
    padding: 7px 15px 6px 15px;
    font-size: 18px;
  }

  ion-title {
    padding-left: 18% !important;
    width: fit-content;
  }
}

ion-content {
  background-color: #323232;

  ion-header {
    margin-left: 3%;
    box-shadow: none;
  }
}

hr {
  background-color: #1E1E1E;
  margin-right: 3%;
  margin-left: 3%;
  margin-bottom: 5%;
}

.selected-header {
  margin-right: auto;
  margin-left: auto;
  width: fit-content;
  padding-top: 20px;
}

.center-header {
  text-align: center;
  font-size: 18px;
}

.subtext {
  color: #aaa;
  font-style: italic;
  font-weight: 400;
  font-size: 14px;
  letter-spacing: 0.03em;
}

ion-content::part(scroll) {
  padding-top: 20px !important;
}

.assign-button {
  font-weight: 500;
  --padding-top: 11px;
  --padding-bottom: 9px;
  --padding-start: 16px;
  --padding-end: 16px;
}

.due-date-item {
  width: 100%;
  height: 100%;
  --background: #323232;
  --inner-padding-end: 0px;
  font-size: 20px;
  font-family: "Exo 2";

  ion-input {
    border: 1px solid #4e4e4e;
    border-radius: 5px;
    margin-top: 0.4em;
    color: white;
    font-size: 16px;
    --padding-start: 8px !important;
    --padding-end: 0px !important;
    caret-color: #7f550c;
    background-color: #1e1e1e;
    margin-top: 0.5em;
    cursor: pointer;
  }

  ion-icon {
    cursor: pointer;
  }

  .calendar-icon {
    position: absolute;
    bottom: 10px;
    right: 5px;
  }
}

.scormloader {
  background: rgba(17, 17, 17, 0.5);
  height: 100%;
  position: absolute;
  width: 100%;
  top: 0px;

  ion-row {
    height: 100%;

    ion-col {
      text-align: center;
      color: #f99e00;
    }
  }
}