/*
 * App Global CSS
 * ----------------------------------------------------------------------------
 * Put style rules here that you want to apply globally. These styles are for
 * the entire app and not just one component. Additionally, this file can be
 * used as an entry point to import other CSS/Sass files to be included in the
 * output CSS.
 * For more information on global stylesheets, visit the documentation:
 * https://ionicframework.com/docs/layout/global-stylesheets
 */

/* Core CSS required for Ionic components to work properly */
@import '~@ionic/angular/css/core.css';

/* Basic CSS for apps built with Ionic */
@import '~@ionic/angular/css/normalize.css';
@import '~@ionic/angular/css/structure.css';
@import '~@ionic/angular/css/typography.css';
@import '~@ionic/angular/css/display.css';

/* Optional CSS utils that can be commented out */
@import '~@ionic/angular/css/padding.css';
@import '~@ionic/angular/css/float-elements.css';
@import '~@ionic/angular/css/text-alignment.css';
@import '~@ionic/angular/css/text-transformation.css';
@import '~@ionic/angular/css/flex-utils.css';

:root {
  --color-primary: #ee9907;
  // General Page Margins
  --page-margin-left: 3.5vw;
  --page-margin-right: 2vw;
  --page-margin-left-player: 3.5vw;
  --page-margin-right-player: 2vw;
  --page-margin-left-header: 15px;
  --page-margin-right-header: 15px;
  --page-margin-left-player-header: 15px;
  --page-margin-right-player-header: 2vw;
  // General Line Height
  --general-line-height: 1.1;
}

// Cookie Alert styles
.cc-theme-edgeless.cc-window {
  z-index: 9999999;
}

// Hide toast when loading overlay is displayed
ion-toast.toast-hidden {
  opacity: 0 !important;
  pointer-events: none !important;
  visibility: hidden !important;
}

// Generated parsed text style
.center-question {
  p {
    margin: 16px 0px;
  }
}

.sub-heading {
  p {
    margin: 3px 0px 9px 0px;
  }
}

.ion-color.sc-ion-searchbar-md-h {
  color: var(--ion-color-contrast);
  padding-left: 0px;
}

h1 {
  margin: 0px;
}

body {
  background-color: #222428;
  color: rgba(0, 0, 0, 0.87);
}

ion-grid {
  margin: 0;
  padding: 0;
  --ion-background-color: transparent;

  ion-row {
    margin: 0;
    padding: 0;
    --ion-background-color: transparent;

    ion-col {
      margin: 0;
      padding: 0;
      --ion-background-color: transparent;
    }
  }
}

ion-modal {
  .ion-page {
    background-color: #222428 !important;
  }
}

.alert-wrapper {
  .alert-title {
    color: white;
  }

  background-color: #222428 !important;
}

.html-alert .alert-message {
  white-space: pre-line;
}

ion-select-popover {
  background-color: #181818 !important;

  ion-list {
    padding-top: 0px !important;
    padding-bottom: 0px !important;
    background-color: #181818 !important;
  }

  ion-item::part(native) {
    background-color: #181818 !important;
    color: white;
  }
}

ion-checkbox::part(container) {
  --background: none;
  --background-checked: var(--ion-color-primary);
  --border-color: #ffffff;
  --border-color-checked: var(--ion-color-primary);
}

ion-content {
  --ion-background-color: transparent;
  margin: 0;
  padding: 0;
}

.ion-page {
  background-color: transparent;
}

ion-router-outlet {
  --ion-background-color: transparent;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 6, 1);
}

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background: rgba(23, 23, 23, 0);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: rgba(69, 69, 69, 1);
  border-radius: 8px;
  width: 8px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(60, 60, 60);
}

.disabled {
  // display: contents !important;
  opacity: 0.5 !important;
}

.hidden {
  // display: contents !important;
  display: none;
}

.font-nunito {
  font-family: Nunito;
}

.font-play {
  font-family: Play;
}

.font-exo2 {
  font-family: 'Exo 2';
}

// Fonts ---------------------------------------------------------------------------------------------
// Instance Page Title
.instance-title-default {
  font-family: 'Exo 2';
  font-weight: 800;
  letter-spacing: 0, 01em;
  line-height: 1.1;
  color: #fff;
  margin-left: 3.5vw;
}

.instance-title-sticky {
  font-family: 'Exo 2';
  font-weight: 800;
  letter-spacing: 0, 01em;
  line-height: 1.1;
  color: #fff;
}

// Row Title
.row-title {
  font-family: 'Exo 2';
  font-weight: 800;
  letter-spacing: 0, 01em;
  line-height: 1.1;
  color: #fff;
}

.description {
  font-family: 'Roboto';
  font-weight: 400;
  letter-spacing: 0, 03em;
  line-height: 1.3;
  font-size: 1.125em;
  color: #ccc;
}

//Instance Label
.instance-label {
  font-family: 'Roboto';
  font-weight: 400;
  letter-spacing: 0, 03em;
  line-height: 1.4;
  font-size: 1.125em;
  color: #aaa;
}

// ---------------------------------------------------------------------------------------------------
// ngx-quil
.ql-editor.ql-blank::before {
  color: rgba(255, 255, 255, 0.699) !important;
}

.ql-container.ql-snow {
  width: 100%;
}

li {
  padding-left: 10px !important;
}

ol li::before {
  color: #fff;
}

.mat-mdc-dialog-container {
  box-shadow:
    0px 11px 15px -7px rgb(0 0 0 / 20%),
    0px 24px 38px 3px rgb(0 0 0 / 14%),
    0px 9px 46px 8px rgb(0 0 0 / 12%);
  background: #181818;
  color: white;
}

.popover-content {
  width: 500px;
}

.tag-popover {
  --background: #1e1e1e;
  --width: 20%;
  --max-height: 30%;

  ion-content {
    .back-nav {
      margin-left: 0px !important;
    }
  }
}

.chip-option-select-popover {
  --width: 20%;
  --max-height: 30%;

  ion-list {
    padding-top: 0px !important;
    padding-bottom: 0px !important;

    ion-item:hover {
      cursor: pointer;
      --background: rgba(39, 39, 39);
    }
  }

  .cancel-col {
    display: flex;
    justify-content: flex-start;
    background-color: rgba(30, 30, 30);
    padding-left: 10px;
  }

  .save-col {
    display: flex;
    justify-content: flex-end;
    background-color: rgba(30, 30, 30);
    padding-right: 10px;
  }
}

.settings-popover {
  --background: #222428;
}

.row-add-popover {
  --background: black;
  --offset-y: 40px;
  --width: 62%;
}

.product-add-popover {
  --background: black;
  --width: 30%;
  --height: 40%;
}

.criteria-add-popover {
  --height: auto;
  --max-height: 30%;
  --background: rgb(35, 35, 35);
  --width: 25%;
}

.amp-content-title {
  display: none;
}

/* Modal and Popover Controller css will not work if not placed in Global.scss

  /* Popover Interface: set color for the popover using Item's CSS variables */
.my-custom-interface .select-interface-option {
  --color: #fff;
  --color-hover: #db8b00;
  --background: #181818;
}

.row-add-custom-popover {
  --offset-x: 10px;
  --offset-y: 10px;
}

.add-organization-modal {
  align-items: center;
  justify-content: center;

  &::part(content) {
    width: 50%;
    height: 40%;
    background: transparent;
  }

  .add-media-popover {
    &::part(content) {
      width: 10%;
      background: black;
      padding: 0.5em;
      align-items: center;
      justify-content: center;
    }
  }
}

.repo-dashboard-dialog {
  position: absolute;
  align-items: center;
  justify-content: center;

  &::part(content) {
    width: 25%;
    height: 40%;
  }
}

.persona-dialog {
  position: absolute;
  align-items: center;
  justify-content: center;

  &::part(content) {
    width: 350px;
    height: fit-content;
    border-radius: 5px;
    border: 1px solid black;
    background-color: #333333;
  }
}

@media screen and (max-width: 1000px) {
  .user-persona-dialog {
    &::part(content) {
      width: 85vw;
    }
  }
}

@media screen and (min-width: 1000px) {
  .user-persona-dialog {
    &::part(content) {
      width: 525px;
    }
  }
}

.user-persona-dialog {
  box-shadow:
    0px 11px 15px -7px rgb(0 0 0 / 20%),
    0px 24px 38px 3px rgb(0 0 0 / 14%),
    0px 9px 46px 8px rgb(0 0 0 / 12%);
  background: rgba(000, 000, 000, 0.75);
  position: absolute;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  &::part(content) {
    min-height: calc(100vh - 95px);
    max-height: calc(100vh - 95px);
    border-radius: 15px;
    background-color: #000;
    border: 2px solid #333;
  }
}

.user-organizations-modal {
  box-shadow:
    0px 11px 15px -7px rgb(0 0 0 / 20%),
    0px 24px 38px 3px rgb(0 0 0 / 14%),
    0px 9px 46px 8px rgb(0 0 0 / 12%);
  background: rgba(000, 000, 000, 0.75);
  position: absolute;
  align-items: center;
  justify-content: center;

  &::part(content) {
    height: 50vh;
    width: 50vw;
    border-radius: 15px;
    background-color: #000;
    border: 2px solid #333;
  }
}

.join-dialog {
  position: absolute;
  align-items: center;
  justify-content: center;

  &::part(content) {
    width: 380px;
    height: 720px;
    border-radius: 5px;
    border: 1px solid black;
    background-color: #333333;
  }
}

.join-dialog-no-orgs {
  position: absolute;
  align-items: center;
  justify-content: center;
  z-index: 500;

  &::part(content) {
    width: 380px;
    height: 220px;
    border-radius: 5px;
    border: 1px solid black;
    background-color: #333333;
  }
}

ion-modal.full-screen .ion-page {
  background-color: transparent !important;
}

.full-screen {
  position: absolute;
  align-items: center;
  justify-content: center;

  &::part(content) {
    width: 100vw;
    height: 100vh;
    background-color: rgb(0 0 0 / 80%) !important;
  }
}

.confirm-dialog {
  --width: 465px;
  --height: fit-content !important;
}

.select-user-role-modal {
  position: absolute;
  align-items: center;
  justify-content: center;
  z-index: 500;

  &::part(content) {
    width: 380px;
    height: 300px;
    border-radius: 5px;
    border: 1px solid black;
    background-color: #333333;
  }
}

.update-user-role-modal {
  position: absolute;
  align-items: center;
  justify-content: center;

  &::part(content) {
    width: 420px;
    height: 60px;
    border-radius: 5px;
    border: 1px solid black;
    background-color: #333333;
  }
}

.product-history-modal {
  position: absolute;
  align-items: center;
  justify-content: center;

  &::part(content) {
    width: 420px;
    height: fit-content;
    min-height: 330px;
    border-radius: 10px;
    border: 1px solid black;
    background-color: #333333;
  }
}

.add-search-modal {
  --offset-y: -20px;
  --offset-x: 24px;

  &::part(content) {
    max-width: 350px;
    width: 100%;
    border-radius: 11px;
    border: 1px solid black;
    padding: 0px;
  }
}

.create-entity-modal {
  &::part(content) {
    width: 400px;
    height: auto;
    border-radius: 5px;
    border: 1px solid black;
    background-color: #333333;
  }
}

.class-assignment-modal {
  position: absolute;
  align-items: end;
  justify-content: center;
  padding-bottom: 15px;
  overflow: visible;

  &::part(content) {
    width: fit-content;
    min-width: 400px;
    height: fit-content;
    border-radius: 11px;
    border: 1px solid black;
    background-color: #333333;
  }
}

app-class-assignment-form {
  align-items: center;
  justify-content: center;
  height: 695px;
  width: 532px;

  &::part(content) {
    width: 532px;
    min-width: 400px;
    border-radius: 4px;
    height: fit-content;
    background-color: #333333;
    border: none;
  }
}

.add-product-org-modal,
.my-instances-dialog {
  position: absolute;
  align-items: center;
  justify-content: center;

  &::part(content) {
    width: fit-content;
    min-width: 400px;
    height: fit-content;
    max-height: fit-content;
    border-radius: 11px;
    border: 1px solid black;
    background-color: #333333;
  }
}

.no-backdrop-popover::part(backdrop) {
  background: transparent;
}

.completion-modal {
  position: absolute;
  align-items: center;
  justify-content: center;

  &::part(content) {
    width: 650px;
    height: auto;
    border-radius: 10px;
    border: 2px solid rgb(64, 64, 64);
    background-color: #333333;
  }
}

.share-modal-container {
  &::part(content) {
    width: 562px;
    height: 481px;
    border-radius: 8px;
    border: 2px solid black;
    background-color: #111111;
  }
}

ion-icon[name='information-circle-outline'] {
  display: none !important;
}

.mat-mdc-tooltip {
  font-size: 20px !important;
  background-color: #181818;
  max-width: unset !important;
}

ion-toast.success-toast {
  --background: #232428;
  --box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, 0.2);
  color: #fff;
}

ion-toast.success-toast::part(container) {
  border: 2px solid green;
  border-radius: 5px;
}

ion-toast.success-toast::part(icon) {
  color: green;
}

ion-toast.success-toast::part(button) {
  color: #f99e00;
}

/* Link copied toast style */
ion-toast.link-copied-toast {
  --background: #333333;
  --box-shadow: 0px 2px 8px 0 rgba(0, 0, 0, 0.3);
  color: #fff;
  bottom: 30px;
  --width: fit-content;
  --height: 40px;
  --border-radius: 500px;
}

ion-toast.link-copied-toast::part(container) {
  padding: 5px 4px;
  max-width: 220px;
  margin: 0px !important;
}

ion-toast.link-copied-toast::part(message) {
  display: flex;
  align-items: center;
  padding-bottom: 8px;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0.2px;
}

ion-toast.link-copied-toast::part(button) {
  display: none;
}

ion-toast.completed-row-progress-toast {
  display: flex;
  justify-content: center;
  --width: fit-content;
  --background: transparent;
  --box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, 0.2);
  color: #fff;
  left: 10vw !important;
  right: 23vw !important;
  width: auto !important;
  bottom: 10px;
  background-image: linear-gradient(to top, #181818 0%, transparent 100px);
  border-bottom: 10px solid #181818;
}

@media screen and (min-width: 1500px) {
  ion-toast.completed-row-progress-toast {
    left: 8vw !important;
    right: 20vw !important;
  }
}

@media screen and (min-width: 1700px) {
  ion-toast.completed-row-progress-toast {
    left: 7vw !important;
    right: 20vw !important;
  }
}

@media screen and (max-width: 1200px) {
  ion-toast.completed-row-progress-toast {
    left: 12vw !important;
    right: 25vw !important;
  }
}

@media screen and (max-width: 1000px) {
  ion-toast.completed-row-progress-toast {
    left: 12vw !important;
    right: 25vw !important;
  }
}

@media screen and (max-width: 992px) {
  ion-toast.completed-row-progress-toast {
    --box-shadow: none;
  }

  ion-toast.completed-row-progress-toast {
    left: 20px !important;
    right: clamp(250px, 30%, 575px) !important;
    background: none;
  }
}

@media screen and (max-width: 767px) {
  ion-toast.completed-row-progress-toast {
    left: 20px !important;
    right: 20px !important;
    background: none;
  }
}

ion-toast.completed-row-progress-toast::part(message) {
  font-size: 0.8em;
  font-weight: 500;
  padding-inline: 12px;
  padding-inline-end: 0px;
  letter-spacing: 0.03em;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

ion-toast.completed-row-progress-toast::part(container) {
  border: green solid 2px;
  border-radius: 7px;
  background: #1f281d;
}

ion-toast.completed-row-progress-toast::part(icon) {
  color: green;
}

ion-toast.completed-row-progress-toast::part(button) {
  color: #000;
  background-color: #f99e00;
  border-radius: 5px;
  font-weight: 600;
}

@media screen and (max-width: 992px) {
  ion-toast.completed-row-progress-toast::part(container) {
    margin-bottom: 80px;
  }
}

ion-toast.started-row-progress-toast {
  display: flex;
  justify-content: center;
  --width: fit-content;
  --background: transparent;
  color: #e0e0e0;
  --background-hover: none;
  --box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, 0.2);
  left: 10vw !important;
  right: 23vw !important;
  width: auto !important;
  bottom: 10px;
  background-image: linear-gradient(to top, #181818 0%, transparent 100px);
  border-bottom: 10px solid #181818;
}

@media screen and (min-width: 1500px) {
  ion-toast.started-row-progress-toast {
    left: 8vw !important;
    right: 20vw !important;
  }
}

@media screen and (min-width: 1700px) {
  ion-toast.started-row-progress-toast {
    left: 7vw !important;
    right: 20vw !important;
  }
}

@media screen and (max-width: 1200px) {
  ion-toast.started-row-progress-toast {
    left: 12vw !important;
    right: 25vw !important;
  }
}

@media screen and (max-width: 1000px) {
  ion-toast.started-row-progress-toast {
    left: 12vw !important;
    right: 25vw !important;
  }
}

@media screen and (max-width: 992px) {
  ion-toast.started-row-progress-toast {
    --box-shadow: none;
  }

  ion-toast.started-row-progress-toast {
    left: 20px !important;
    right: clamp(250px, 25%, 575px) !important;
    background: none;
  }
}

@media screen and (max-width: 767px) {
  ion-toast.started-row-progress-toast {
    left: 20px !important;
    right: 20px !important;
    background: none;
  }
}

ion-toast.started-row-progress-toast::part(message) {
  font-size: 1.125em;
  padding-inline: 12px;
  padding-inline-end: 0px;
  font-weight: 500;
  letter-spacing: 0.03em;
}

ion-toast.started-row-progress-toast::part(container) {
  width: fit-content;
  border: #bb7000 solid 2px;
  border-radius: 7px;
}

@media screen and (max-width: 992px) {
  ion-toast.started-row-progress-toast::part(container) {
    margin-bottom: 80px;
  }
}

ion-toast.not-started-row-progress-toast {
  display: flex;
  justify-content: center;
  --width: fit-content;
  --background: transparent;
  color: #e0e0e0;
  --background-hover: none;
  --box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, 0.2);
  left: 10vw !important;
  right: 23vw !important;
  width: auto !important;
  bottom: 10px;
  background-image: linear-gradient(to top, #181818 0%, transparent 100px);
  border-bottom: 10px solid #181818;
}

@media screen and (min-width: 1500px) {
  ion-toast.not-started-row-progress-toast {
    left: 8vw !important;
    right: 20vw !important;
  }
}

@media screen and (min-width: 1700px) {
  ion-toast.not-started-row-progress-toast {
    left: 7vw !important;
    right: 20vw !important;
  }
}

@media screen and (max-width: 1200px) {
  ion-toast.not-started-row-progress-toast {
    left: 12vw !important;
    right: 25vw !important;
  }
}

@media screen and (max-width: 1000px) {
  ion-toast.not-started-row-progress-toast {
    left: 12vw !important;
    right: 25vw !important;
  }
}

@media screen and (max-width: 992px) {
  ion-toast.not-started-row-progress-toast {
    --box-shadow: none;
  }

  ion-toast.not-started-row-progress-toast {
    left: 20px !important;
    right: clamp(250px, 25%, 575px) !important;
    background: none;
  }
}

@media screen and (max-width: 767px) {
  ion-toast.not-started-row-progress-toast {
    left: 20px !important;
    right: 20px !important;
    background: none;
  }
}

ion-toast.not-started-row-progress-toast::part(message) {
  font-size: 1.125em;
  padding-inline: 12px;
  padding-inline-end: 0px;
  font-weight: 500;
  letter-spacing: 0.03em;
}

ion-toast.not-started-row-progress-toast::part(container) {
  width: fit-content;
  border: rgb(121, 121, 121) solid 2px;
  border-radius: 7px;
}

@media screen and (max-width: 992px) {
  ion-toast.not-started-row-progress-toast::part(container) {
    margin-bottom: 80px;
  }
}

ion-toast.class-assignment-toast {
  --background: #232428;
  --box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, 0.2);
  color: #fff;
  --background-hover: none;
  transform: translateY(-50px) !important;
}

ion-toast.class-assignment-toast::part(container) {
  border: 1px solid #f99e00;
  border-radius: 5px;
}

ion-toast.class-assignment-toast::part(icon) {
  color: #f99e00;
}

ion-toast.class-assignment-toast::part(button) {
  color: #f99e00;
}

ion-toast.error-toast {
  --background: #232428;
  --box-shadow: 3px 3px 10px 0 rgba(0, 0, 0, 0.2);
  --color: #fff;
}

ion-toast.error-toast::part(container) {
  border: 1px solid red;
  border-radius: 5px;
}

ion-toast.error-toast::part(icon) {
  color: red;
}

ion-toast.error-toast::part(button) {
  color: red;
}

.badge-link {
  cursor: pointer !important;
}

// .mdc-tab__content {
//   color: #aaa !important;
// }

// .mdc-tab--active {
//   color: #f99e00 !important;
// }

// .mdc-tab__content {
//   font-weight: 700;
//   letter-spacing: 0.02em;
//   opacity: 100%;
// }

.mat-mdc-slide-toggle {
  margin-left: 20px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.mat-mdc-slide-toggle .mdc-label {
  color: white !important;
}

.mdc-text-field--filled:not(.mdc-text-field--disabled) {
  background-color: #222222 !important;
}

// FOR STYLING ON THE BUILDER PAGE
// .component-overlay {
//   display: none;
// }

.mat-mdc-standard-chip {
  background-color: #181818 !important;
}

.mdc-evolution-chip__text-label {
  color: white !important;
}

.mdc-evolution-chip__icon--trailing {
  color: #747474 !important;
}

.riasec-score {
  .ngx-charts .gridline-path {
    stroke: #181818 !important;
  }

  .polar-chart .polar-chart-background {
    fill: #242424 !important;
  }

  .ngx-charts text {
    fill: white !important;
  }
}

#hubspot-messages-iframe-container {
  min-height: 96px;
  min-width: 100px;
  width: 96px;
  height: 100px;
  color-scheme: light;
  margin-right: 7px;
}

.status-foot-container {
  position: absolute;
  left: 25px;
  z-index: 999;
  bottom: -8px;
  width: 100px;
  height: 26px;

  .inner {
    padding: 2px 5px 1px 5px;
    font-size: 10px;
    border-radius: 3px;
    color: #fff;
    letter-spacing: 0.1em;
    line-height: normal;
    width: max-content;
    display: inline-block;
    font-weight: bold;
  }
}

.status-inprogress {
  outline: 1px solid orange !important;
  border-radius: 7px;
  margin-bottom: 6px;
}

.status-complete {
  outline: 1px solid green !important;
  height: 100%;
  border-radius: 7px;
}

access-widget-ui::part(acsb-trigger) {
  width: 60px !important;
  height: 60px !important;
}

.cc-window.cc-banner {
  align-items: center;
  justify-content: center;
}

.cc-btn {
  margin-top: 9px !important;
  margin-bottom: 7px !important;
  margin-right: 30px !important;
  margin-left: 30px !important;
  border-radius: 4px;
  padding-top: 9px !important;
  padding-bottom: 7px !important;
  padding-right: 15px !important;
  padding-left: 15px !important;
}

.conveythis-widget-main {
  width: 90px !important;
}

#conveythis-wrapper {
  width: 90px !important;
}

@media screen and (max-width: 992px) {
  access-widget-ui::part(acsb-trigger) {
    display: none;
  }

  .instance-label {
    font-size: 0.75em;
  }

  #conveythis-wrapper {
    display: none;
    visibility: hidden;
    bottom: 90px !important;
  }
}

.conveythis-widget-language {
  padding-left: 5px !important;
}

.full-parent-height {
  height: 100%;
}

.feature-type-select-popover {
  --width: 400px;
}

.custom-popover-center {
  &::part(content) {
    top: 50% !important;
    left: 50% !important;
    height: fit-content;
    max-height: fit-content;
    transform: translate(-50%, -50%) !important;
  }
}

// iOS DateTime Dark Theme Global Styles
.ios {
  // Target all datetime components globally for iOS
  ion-datetime {
    color: white !important;
    --background: #181818 !important;
    --background-rgb: 24, 24, 24 !important;

    // iOS wheel picker styling
    --wheel-highlight-background: #7f550c !important;
    --wheel-fade-background-rgb: 24, 24, 24 !important;
    --wheel-text-color: white !important;
    --wheel-highlight-border-radius: 8px !important;

    // Calendar mode styling - darker background
    --calendar-background: #181818 !important;
    --calendar-background-rgb: 24, 24, 24 !important;
    --calendar-text-color: #e0e0e0 !important;
    --calendar-text-rgb: 224, 224, 224 !important;
    --title-color: #ffffff !important;
    --calendar-day-name-text-color: #b0b0b0 !important;
    --highlight-color: #f99e00 !important;
    --highlight-color-rgb: 249, 158, 0 !important;
    --today-text-color: #e0e0e0 !important;
    --calendar-nav-color: #f99e00 !important;

    // Header and navigation - darker
    --header-background: #181818 !important;
    --header-color: #ffffff !important;

    // Text color overrides
    --ion-color-base: #f99e00 !important;
    --ion-text-color: #e0e0e0 !important;

    // Additional iOS specific properties
    --ion-color-step-50: #181818 !important;
    --ion-color-step-100: #181818 !important;
    --ion-color-step-150: #181818 !important;
    --ion-color-step-200: #181818 !important;

    // Calendar day styling using ::part()
    &::part(calendar-day) {
      color: #e0e0e0 !important;
      font-size: 12px !important;
      padding: 4px !important;
      margin: 2px !important;
    }

    ion-picker-column {
      font-size: 12px !important;
    }

    &::part(calendar-day active) {
      background: transparent !important;
      border: 1px solid var(--ion-color-base) !important;
      color: var(--ion-color-base) !important;
    }

    &::part(ion-picker-column) {
      font-size: 12px !important;
    }

    &::part(calendar-day-selected) {
      background-color: transparent !important;
      color: #ffffff !important;
      border: 2px solid #ffff00 !important;
      border-radius: 50% !important;
      box-shadow: 0 0 0 1px #ffff00 !important;
    }

    // Header text
    &::part(month-year) {
      font-size: 16px !important;
    }

    // Day names
    &::part(calendar-day-name) {
      font-size: 12px !important;
      padding: 2px !important;
    }
  }

  // Ensure popovers containing datetime are also dark
  ion-popover {
    height: 250px;
    --background: #181818 !important;
    --color: white !important;

    &::part(content) {
      --background: #181818 !important;
      --ion-background-color: #181818 !important;
      --color: white !important;
      background: #181818 !important;
    }

    ion-content {
      --background: #181818 !important;
      --color: white !important;
      background: #181818 !important;
    }

    // Specifically target datetime inside popovers
    ion-datetime {
      color: white !important;
      --background: #181818 !important;
      --ion-background-color: #181818 !important;
    }
  }
}
