<button id="my-trigger"></button>
@if (actionType === addToDialogActionTypes.SelectClass) {
  <app-class-selector [instanceId]="instanceId" [instances]="instances" (instanceSelected)="selectInstance($event)" (createClicked)="addInstance($event)" (closeClicked)="close()"></app-class-selector>
} @else if (actionType === addToDialogActionTypes.SelectAssignment && selectedInstance) {
  <app-assignment-selector
    [selectedInstance]="selectedInstance"
    [instanceId]="instanceId"
    [instances]="instances"
    [lastModified]="lastModified ?? []"
    [loading]="loading"
    [assignments]="assignments?.instances ?? []"
    [selectedAssignment]="selectedAssignment"
    (instanceSelected)="selectInstance($event)"
    (assignmentSelected)="assignInstance($event.id, instanceId, false, 'assignment')"
    (createClicked)="addAssignmentClicked($event)"
    (closeClicked)="close()"></app-assignment-selector>
} @else if (actionType === addToDialogActionTypes.CreateClass && orgId) {
  <app-class-assignment-form [featureTypeName]="'Modifiable Learning Container Pages'" [orgId]="orgId" [loading]="loading" (createClicked)="createInstance($event)" (closeClicked)="close()"></app-class-assignment-form>
} @else if (actionType === addToDialogActionTypes.CreateAssignment && selectedInstance) {
  <app-class-assignment-form
    [featureTypeName]="'Accredited Learning Container Pages'"
    [orgId]="orgId"
    [loading]="loading"
    [header]="'Assignment'"
    (createClicked)="createInstance($event)"
    (closeClicked)="close()"></app-class-assignment-form>
} @else if (actionType === addToDialogActionTypes.EditClass && selectedInstance) {
  <app-class-assignment-form
    [featureTypeName]="'Modifiable Learning Container Pages'"
    [instance]="selectedInstance"
    [orgId]="selectedInstance.organizationId ?? ''"
    [loading]="loading"
    (createClicked)="editInstance($event)"
    (closeClicked)="close()"></app-class-assignment-form>
} @else if (actionType === addToDialogActionTypes.EditAssignment && selectedInstance) {
  <app-class-assignment-form
    [featureTypeName]="'Accredited Learning Container Pages'"
    [orgId]="selectedInstance.organizationId ?? ''"
    [instance]="selectedInstance"
    [loading]="loading"
    [header]="'Assignment'"
    (createClicked)="editInstance($event)"
    (closeClicked)="close()"></app-class-assignment-form>
} @else if (actionType === addToDialogActionTypes.ConfirmClassSelection && selectedInstance) {
  <app-class-selection-confirmation
    [selectedInstance]="selectedInstance"
    [instanceId]="instanceId"
    [instances]="instances ?? []"
    (instanceSelected)="assignInstance(selectedInstance.id, instanceId, false, 'class')"
    (closeClicked)="close()"></app-class-selection-confirmation>
} @else if (actionType === addToDialogActionTypes.SuccessClass && selectedInstance) {
  <app-success-message [itemName]="selectedInstance.title" [typeName]="'Class'" (closeClicked)="successClose()"></app-success-message>
} @else if (actionType === addToDialogActionTypes.SuccessAssignment && selectedInstance) {
  <app-success-message [itemName]="selectedAssignment?.title ?? newAssignmentTitle ?? ''" [typeName]="'Assignment'" (closeClicked)="successClose(true, $event)"></app-success-message>
}
