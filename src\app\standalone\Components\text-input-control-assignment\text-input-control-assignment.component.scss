.parent-container {
  ion-text {
    color: red;
    font-style: italic;
    font-size: 14px;
  }

  ion-button {
    margin-right: 10px;
  }

  ion-label {
    cursor: help !important;
  }

  ion-icon {
    cursor: help !important;
  }

  .identifier {
    background-color: #7f550c;
    border-radius: 0.2em;
    color: black;
    font-weight: bold;
    padding: 0.3em;
    margin-left: 0.4em;
  }

  .reqAsterisk {
    color: #7f550c;
    font-size: 28px;
  }

  .no-padding {
    --inner-padding-end: 0;
    --padding-start: 0;
  }

  .no-border {
    ion-input {
      height: 100%;
      width: 100%;
      border: none !important;
    }
  }

  .side-panel-input-padding {
    --inner-padding-end: 10px;
    --padding-start: 10px;
  }
}

.normal {
  ion-item {
    --border-color: transparent;
    --color: white;
    font-size: 18px;
    font-family: 'Exo 2', sans-serif;
    --background: transparent;
    background: transparent;
    box-shadow: none;
    border-radius: 12px;
    padding: 0;
    margin-bottom: 8px;
  }

  ion-input {
    border: none;
    border-radius: 12px;
    margin-top: 0.2em;
    color: #d1d1d1;
    font-size: 18px;
    background: #232326;
    padding: 18px 18px 12px 18px;
    box-shadow: none;
    outline: none;
    width: 100%;
    font-family: 'Exo 2', sans-serif;
    caret-color: #f99e00;
    transition: background 0.2s, border 0.2s;
    font-style: italic;

    &::placeholder {
      color: #b0b0b0;
      opacity: 1;
      font-style: italic;
      font-size: 17px;
    }
  }

  .label-header {
    margin-bottom: 2px;
    padding-bottom: 0;
    font-weight: 400;
    letter-spacing: 0.01em;
    color: #b0b0b0;
    font-size: 13px;
    background: transparent;
    padding-left: 2px;
    padding-top: 8px;
    font-family: 'Exo 2', sans-serif;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .reqAsterisk {
    color: #f99e00;
    font-size: 13px;
    margin-left: 2px;
    vertical-align: top;
  }
}

.selected {
  .label-header {
    width: fit-content;
    padding: 2px 11px 10px 11px;
    background: #f99e00;
    color: #000000;
    border-color: #f99e00;
    border-width: 1px;
    border-style: solid;
    border-radius: 3px 3px 0px 0px;
    font-family: 'Roboto', sans-serif;
    font-weight: bold;
    font-size: 15px;
    line-height: 1.1;
    letter-spacing: 0.3px;
    text-align: left;
  }

  .inner-container {
    --border-color: transparent;
    --color: white;
    font-size: 18px;
    font-family: 'Roboto', sans-serif;
    --background: transparent;
    background: transparent;
    width: 100%;
    --inner-padding-end: 0;
    --padding-start: 0;
    border-radius: 12px;
    box-shadow: none;
    padding: 0;
  }

  ion-input {
    border: none;
    border-radius: 12px;
    background: #232326;
    color: #d1d1d1;
    font-family: 'Roboto', sans-serif;
    font-weight: 400;
    font-size: 18px;
    line-height: 1.3;
    letter-spacing: 0.5px;
    text-align: left;
    padding: 20px 18px 12px 18px;
    box-shadow: none;
    outline: none;
    width: 100%;
    caret-color: #f99e00;
    transition: background 0.2s, border 0.2s;
    font-style: italic;

    &::placeholder {
      color: #b0b0b0;
      opacity: 1;
      font-style: italic;
      font-size: 17px;
    }
  }

  .reqAsterisk {
    color: #b07a00;
    font-size: 13px;
    margin-left: 2px;
    vertical-align: top;
  }
}

.password-container {
  display: flex;
  width: 100%;
  align-items: center;
  border: 1px solid #4e4e4e;
  border-radius: 0px 3px 3px 3px;
  margin-top: 5px;

  ion-input {
    border: none !important;
  }

  .type-toggle {
    padding-inline-start: 0.5rem;
    margin-right: 10px;
    color: #7a7a7a !important;
    cursor: pointer !important;

    .show-option,
    .hide-option {
      font-size: 1.2rem;
      display: block;
    }
  }
}

.floating-input-container {
  position: relative;
  width: 500px;
  height: 56px;
  display: flex;
  align-items: center;
  background: #232326;
  border-radius: 4px 4px 0 0;
  border-bottom: 1px solid #96959D !important;
  box-sizing: border-box;
  overflow: hidden;
}

.floating-input {
  width: 100%;
  height: 100%;
  border: none !important;
  outline: none;
  background: transparent;
  color: #d1d1d1;
  font-size: 18px;
  font-family: 'Exo 2', sans-serif;
  padding: 24px 16px 8px 16px;
  box-sizing: border-box;
}

.floating-label {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #b0b0b0;
  font-size: 15px;
  font-family: 'Exo 2', sans-serif;
  pointer-events: none;
  transition: all 0.2s cubic-bezier(0.4,0,0.2,1);
  background: transparent !important;
}

.floating-input-container.focused .floating-label,
.floating-input-container .floating-input:not(:placeholder-shown) + .floating-label {
  top: 8px;
  font-size: 12px;
  color: #96959D;
  background: transparent !important;
  padding: 0 4px;
  transform: none;
}

.floating-input:disabled {
  background: #232326;
  color: #888;
}

.floating-input::placeholder {
  color: #b0b0b0;
  opacity: 1;
  font-style: italic;
}

.floating-label .reqAsterisk {
  color: #f99e00;
  font-size: 13px;
  margin-left: 2px;
  vertical-align: top;
}

@media screen and (max-width: 960px) {
  .label-header {
    overflow: hidden;
    text-wrap: nowrap;
    text-overflow: ellipsis;
  }
}