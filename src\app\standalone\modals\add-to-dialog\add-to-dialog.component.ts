import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { IFeatureType, IInstance, IInstanceIn, IMyInstanceResult, IRepositoryBuilderDashboard } from '@app/core/contracts/contract';
import { AddToDialogActionTypes } from '@app/core/enums/add-to-dialog-action-types';
import { AuthService } from '@app/core/services/auth-service';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { ParseContentPipe } from '@app/shared/pipes/parse-content';
import { IonicModule, ModalController } from '@ionic/angular';
import { Alert<PERSON>ontroller, PopoverController } from '@ionic/angular/standalone';
import { debounceTime, first, Subject, takeUntil, timer } from 'rxjs';
import { AddSearchModalComponent } from '../add-search-modal/add-search-modal.component';
import { ConfirmationDialogComponent } from '../confirmation-dialog/confirmation-dialog.component';
import { AssignmentSelectorComponent } from './components/assignment-selector/assignment-selector.component';
import { ClassFormComponent } from './components/class-form/class-form.component';
import { ClassSelectionConfirmationComponent } from './components/class-selection-confirmation/class-selection-confirmation.component';
import { ClassSelectorComponent } from './components/class-selector/class-selector.component';
import { SuccessMessageComponent } from './components/success-message/success-message.component';
import { ClassAssignmentFormComponent } from '../class-assignment-form/class-assignment-form.component';

export interface CreateInstanceData {
  orgId?: string;
  featureId: string;
  event: any;
}

@Component({
    selector: 'app-add-to-dialog',
    templateUrl: './add-to-dialog.component.html',
    styleUrls: ['./add-to-dialog.component.scss'],
    imports: [IonicModule, FormsModule, ClassSelectorComponent, AssignmentSelectorComponent, ClassAssignmentFormComponent, ClassSelectionConfirmationComponent, SuccessMessageComponent],
    providers: [PopoverController, ParseContentPipe]
})
export class AddToDialogComponent implements OnInit, OnDestroy {
  @Input() instanceId: string;
  @Input() selectedInstance?: IInstance;
  @Input() featureTypeName: string = '';
  @Input() instanceSelector = false;
  @Input() height: number = 60;
  @Input() actionType: AddToDialogActionTypes = AddToDialogActionTypes.SelectClass;
  @Input() reload = false;

  instances: IInstance[];
  lastModified?: IInstance[];
  assignments?: IMyInstanceResult;
  query: string;
  componentDestroyed$: Subject<boolean> = new Subject();
  selectedAssignment?: IInstance;
  newAssignmentTitle?: string;
  selectedRowId: string | null;
  featureTypes: IFeatureType[];
  featureTypeSelectedId: string | undefined;
  addToDialogActionTypes = AddToDialogActionTypes;
  orgId: string;
  classAssignFeatureTypes = ['Achievement Completion', 'Badge Page', 'Lesson Pages', 'Accredited Learning Container Pages'];
  loading = false;
  parentId: string;
  closeTimer: number = 10000;

  constructor(
    private dataService: DataService,
    private modalController: ModalController,
    private instanceService: InstanceService,
    public authService: AuthService,
    private alertController: AlertController,
    private popOver: PopoverController,
    private breadcrumbService: BreadcrumbService,
    private parseContentPipe: ParseContentPipe,
    private router: Router
  ) {}

  ngOnInit() {
    this.searchMyInstances();

    if (this.actionType === this.addToDialogActionTypes.CreateClass) {
      this.addInstance({ featureId: this.selectedInstance?.feature?.id } as CreateInstanceData);
    }
  }

  searchMyInstances(featureTypeId?: string, instanceId?: string) {
    this.featureTypeSelectedId = featureTypeId;
    this.loading = true;
    this.dataService
      .getMyInstances(this.query, this.instanceId ?? this.selectedInstance?.id, featureTypeId)
      .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
      .subscribe((data: IMyInstanceResult) => {
        this.instances = data.instances;
        this.lastModified = data.lastModified;

        if (instanceId && this.lastModified) {
          const selectedInstance = [...this.instances, ...this.lastModified].find(x => x.id === instanceId);
          if (selectedInstance) {
            this.selectInstance(selectedInstance);
          }
        }

        this.loading = false;
      });
  }

  createInstance(instance: IInstanceIn) {
    this.loading = true;
    this.dataService
      .createInstance(instance)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((data: IRepositoryBuilderDashboard) => {
        if (data) {
          if (this.actionType === this.addToDialogActionTypes.CreateAssignment && this.selectedInstance) {
            this.assignInstance(this.selectedInstance?.id, data.id, false, 'assignment', instance.title);
            if (this.instanceId !== this.selectedInstance?.id) {
              this.assignInstance(data.id, this.instanceId, false, 'assignment', instance.title);
            }
          } else if (this.selectedInstance?.isDefault === true) {
            this.close();
          } else {
            this.query = '';
            this.searchMyInstances(undefined, data.id);
          }
        }
      });
  }

  editInstance(instance: IInstanceIn) {
    this.loading = true;
    if (instance.id) {
      this.dataService
        .updateInstance(instance.id, instance)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.instanceService.hardReload$.next(null);
          this.close();
        });
    }
  }

  close() {
    this.modalController.dismiss(this.reload);
  }

  selectInstance(instance: IInstance, assignmentId?: string) {
    this.selectedInstance = instance;

    this.dataService
      .getInstanceAssignments(this.selectedInstance.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((data: IMyInstanceResult) => {
        this.assignments = data;
        if (this.classAssignFeatureTypes.indexOf(this.featureTypeName) !== -1) {
          if (this.selectedInstance) {
            this.assignInstance(this.selectedInstance.id, this.instanceId, true);
          }
        } else {
          this.actionType = this.addToDialogActionTypes.SelectAssignment;
          if (assignmentId) {
            this.selectedAssignment = this.assignments.instances.find(x => x.id === assignmentId);
          }
        }

        this.loading = false;
      });
  }

  async assignInstance(parentId: string, childId: string, shouldClose = true, typeName = 'class', newAssignmentTitle?: string) {    
    this.dataService
      .getInstanceAssignments(parentId, false)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(async (data: IMyInstanceResult) => {
        const assignments = data;
        this.selectedAssignment = this.assignments?.instances.find(x => x.id === parentId);
        if (newAssignmentTitle) {
          this.newAssignmentTitle = newAssignmentTitle;
        }
        if (typeName === 'class' || typeName === 'assignment') {
          this.modalController.getTop().then(modal => {
            if (modal) {
              modal.classList.remove('my-instances-dialog');
              modal.classList.add('class-assignment-modal');
            }
          });
        }
        const alreadyAssigned = parentId && (assignments?.lastModified?.some(x => x.id === childId) || assignments?.instances?.some(x => x.id === childId));

        if (alreadyAssigned) {
          const modal = await this.modalController.create({
            component: ConfirmationDialogComponent,
            cssClass: 'add-search-modal my-instances-dialog',
            componentProps: {
              headerText: 'Already added',
              bodyText: `This has already been assigned to your ${this.selectedInstance?.title} ${typeName}.`,
              buttonText: `Don't add`,
              cancelButtonText: 'Add anyway',
            },
          });

          modal.onDidDismiss().then((result: any) => {
            
            if (result.role === 'cancel' && this.selectedInstance) {
              this.dataService
                .assignToInstance(parentId, childId)
                .pipe(takeUntil(this.componentDestroyed$))
                .subscribe(() => {
                  this.parentId = parentId;
                  if (typeName === 'class') {
                    this.actionType = this.addToDialogActionTypes.SuccessClass;
                  } else {
                    this.actionType = this.addToDialogActionTypes.SuccessAssignment;
                  }
                  if (shouldClose) {
                    timer(this.closeTimer)
                      .pipe(takeUntil(this.componentDestroyed$))
                      .subscribe(() => this.close());
                  }
                });
            }
          });

          await modal.present();
        } else if (this.selectedInstance && parentId) {
          this.dataService
            .assignToInstance(parentId, childId)
            .pipe(takeUntil(this.componentDestroyed$))
            .subscribe(() => {
              this.parentId = parentId;
              if (typeName === 'class') {
                this.actionType = this.addToDialogActionTypes.SuccessClass;
              } else {
                this.actionType = this.addToDialogActionTypes.SuccessAssignment;
              }
              if (shouldClose) {
                timer(this.closeTimer)
                  .pipe(takeUntil(this.componentDestroyed$))
                  .subscribe(() => this.close());
              }
            });
        }
      });
  }

  successClose(populateClassBreadcrumb = false, location: string = '') {
    if (populateClassBreadcrumb === true && this.selectedInstance && this.selectedInstance?.feature?.featureType?.name === 'Modifiable Learning Container Pages') {
      this.parseContentPipe
        .transform(this.selectedInstance.title, this.selectedInstance.id, null, true)
        .pipe(first())
        .subscribe((result: string) => {
          // Get the base URL (first segment of the path)
          const baseUrl = this.router.url.split('/')[0];
          this.breadcrumbService.addBreadCrumb(
            this.selectedInstance?.id ?? '',
            `${baseUrl}/instance/${this.selectedInstance?.id}/default/default`,
            result,
            null,
            this.selectedInstance?.feature?.featureType?.name ?? ''
          );
          if (location === 'students') {
            this.instanceService.openInstance('instance', this.selectedInstance?.id, 'students & grades', 'default');
          } else {
            this.instanceService.openInstance('instance', this.parentId, 'default', 'default');
          }
          this.close();
        });
    } else if (this.parentId) {
      if (location === 'students') {
        this.instanceService.openInstance('instance', this.selectedInstance?.id, 'students & grades', 'default');
      } else {
        this.instanceService.openInstance('instance', this.parentId, 'default', 'default');
      }
      this.close();
    }
  }

  save(instance: IInstance) {
    this.modalController.dismiss({ rowId: instance.id, selectedInstance: instance.id });
  }

  addClicked(orgId: string) {
    this.orgId = orgId;
    this.actionType = this.addToDialogActionTypes.CreateClass;
  }

  addAssignmentClicked(orgId: string) {
    this.orgId = orgId;
    this.actionType = this.addToDialogActionTypes.CreateAssignment;
  }

  public addInstance(data: CreateInstanceData) {
    this.dataService
      .getMyOrganizations(data.featureId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(async data => {
        const distinctIds = Array.from(new Set(data.map(x => x.id)));
        if (distinctIds.length < 1) {
          await this.presentAlert();
          return;
        }
        if (distinctIds.length > 1) {
          this.addInstanceToOrganization(event);
        } else if (this.actionType === this.addToDialogActionTypes.CreateClass || this.actionType === this.addToDialogActionTypes.SelectClass) {
          this.addClicked(data[0].id);
        }
      });
  }

  private async addInstanceToOrganization(event: any) {
    const popover = await this.popOver.create({
      component: AddSearchModalComponent,
      cssClass: 'add-search-modal custom-popover-center my-instances-dialog',
      componentProps: { linkTypeName: 'Organizations', criteriaType: null, options: null },
      trigger: 'my-trigger',
      side: 'top',
      alignment: 'start',
    });

    popover.onDidDismiss().then((result: any) => {
      if (result.data) {
        if (this.actionType === this.addToDialogActionTypes.CreateClass || (this.actionType === this.addToDialogActionTypes.SelectClass && result?.data?.id)) {
          this.addClicked(result.data.id);
        }
      }
    });

    await popover.present();
  }

  private async presentAlert() {
    const alert = await this.alertController.create({
      cssClass: '',
      header: 'Please Note:',
      message: 'You are not linked to an organization that has access to this feature product.',
      buttons: ['OK'],
    });

    await alert.present();

    await alert.onDidDismiss();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
